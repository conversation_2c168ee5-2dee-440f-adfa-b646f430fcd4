/**
 * 增强型网络系统
 * 集成了预测和插值、空间分区、网络质量监控和带宽控制等高级功能
 */
import { System } from '../core/System';
import type { Entity } from '../core/Entity';
import { Debug } from '../utils/Debug';
import { EventEmitter } from '../utils/EventEmitter';
import { WebRTCConnectionManager } from './WebRTCConnectionManager';
import { MediaStreamManager } from './MediaStreamManager';
import { EntitySyncManager } from './EntitySyncManager';
import { UserSessionManager, UserRole } from './UserSessionManager';
import { NetworkEventDispatcher } from './NetworkEventDispatcher';
import { NetworkEventBuffer } from './NetworkEventBuffer';
import { NetworkQualityMonitor, NetworkQualityData } from './NetworkQualityMonitor';
import { DataCompressor, CompressionAlgorithm, CompressionLevel } from './DataCompressor';
import { ServiceDiscoveryClient } from './ServiceDiscoveryClient';
import { NetworkPredictor, PredictionAlgorithm } from './NetworkPredictor';
import { NetworkAdaptiveController, AdaptiveStrategy, NetworkParamsConfig } from './NetworkAdaptiveController';
import { AdvancedBandwidthController, BandwidthControlStrategy } from './AdvancedBandwidthController';
import { QuadtreePartitioning } from './spatial/QuadtreePartitioning';
import { NetworkEntityComponent } from './components/NetworkEntityComponent';
import { NetworkState } from './types';

// 定义缺失的枚举类型
export enum NetworkEntityType {
  STATIC = 'static',
  DYNAMIC = 'dynamic',
  PLAYER = 'player',
  NPC = 'npc',
  OBJECT = 'object'
}

export enum NetworkEntitySyncMode {
  FULL = 'full',
  DELTA = 'delta',
  PRIORITY = 'priority',
  SPATIAL = 'spatial'
}

/**
 * 增强型网络系统配置
 */
export interface EnhancedNetworkSystemConfig {
  /** 是否启用网络系统 */
  enabled?: boolean;
  /** 本地用户ID */
  localUserId?: string;
  /** 同步间隔（毫秒） */
  syncInterval?: number;
  /** 最大重连尝试次数 */
  maxReconnectAttempts?: number;
  /** 重连间隔（毫秒） */
  reconnectInterval?: number;
  /** 是否启用压缩 */
  enableCompression?: boolean;
  /** 压缩算法 */
  compressionAlgorithm?: CompressionAlgorithm;
  /** 压缩级别 */
  compressionLevel?: CompressionLevel;
  /** 是否启用媒体流 */
  enableMediaStream?: boolean;
  /** 是否启用音频 */
  enableAudio?: boolean;
  /** 是否启用视频 */
  enableVideo?: boolean;
  /** 是否启用屏幕共享 */
  enableScreenShare?: boolean;
  /** 是否启用网络质量监控 */
  enableNetworkQualityMonitor?: boolean;
  /** 是否启用带宽控制 */
  enableBandwidthControl?: boolean;
  /** 带宽控制策略 */
  bandwidthControlStrategy?: BandwidthControlStrategy;
  /** 最大上传带宽（字节/秒） */
  maxUploadBandwidth?: number;
  /** 最大下载带宽（字节/秒） */
  maxDownloadBandwidth?: number;
  /** 是否启用实体同步 */
  enableEntitySync?: boolean;
  /** 是否启用用户会话管理 */
  enableUserSessionManagement?: boolean;
  /** 默认用户角色 */
  defaultUserRole?: UserRole;
  /** 是否启用权限检查 */
  enablePermissionCheck?: boolean;
  /** 是否启用事件缓冲 */
  enableEventBuffer?: boolean;
  /** 是否启用事件日志 */
  enableEventLogging?: boolean;
  /** 是否启用服务发现 */
  enableServiceDiscovery?: boolean;
  /** 服务注册URL */
  serviceRegistryUrl?: string;
  /** 是否启用微服务客户端 */
  enableMicroserviceClient?: boolean;
  /** API网关URL */
  apiGatewayUrl?: string;
  /** 是否使用API网关 */
  useApiGateway?: boolean;
  /** 是否启用预测 */
  enablePrediction?: boolean;
  /** 预测算法 */
  predictionAlgorithm?: PredictionAlgorithm;
  /** 最大预测时间（毫秒） */
  maxPredictionTime?: number;
  /** 是否启用插值 */
  enableInterpolation?: boolean;
  /** 插值因子（0-1） */
  interpolationFactor?: number;
  /** 是否启用空间分区 */
  enableSpatialPartitioning?: boolean;
  /** 空间分区最大深度 */
  spatialPartitioningMaxDepth?: number;
  /** 空间分区最大实体数量 */
  spatialPartitioningMaxEntities?: number;
  /** 是否启用自适应网络控制 */
  enableAdaptiveControl?: boolean;
  /** 自适应策略 */
  adaptiveStrategy?: AdaptiveStrategy;
  /** 是否启用抖动缓冲 */
  enableJitterBuffer?: boolean;
  /** 抖动缓冲大小（毫秒） */
  jitterBufferSize?: number;
  /** 是否启用优先级同步 */
  enablePrioritySync?: boolean;
  /** 是否启用增量同步 */
  enableDeltaSync?: boolean;
}

/**
 * 增强型网络系统
 */
export class EnhancedNetworkSystem extends System {
  /** 配置 */
  private options: Required<EnhancedNetworkSystemConfig>;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 网络状态 */
  private state: NetworkState = NetworkState.DISCONNECTED;

  /** 本地用户ID */
  private localUserId: string | null = null;

  /** 重连尝试次数 */
  private reconnectAttempts: number = 0;

  /** 重连定时器ID */
  private reconnectTimerId: number | null = null;

  /** 同步定时器ID */
  private syncTimerId: number | null = null;

  /** 网络管理器 */
  private networkManager: WebRTCConnectionManager | null = null;

  /** 媒体流管理器 */
  private mediaStreamManager: MediaStreamManager | null = null;

  /** 实体同步管理器 */
  private entitySyncManager: EntitySyncManager | null = null;

  /** 用户会话管理器 */
  private userSessionManager: UserSessionManager | null = null;

  /** 网络事件调度器 */
  private eventDispatcher: NetworkEventDispatcher | null = null;

  /** 网络事件缓冲 */
  private eventBuffer: NetworkEventBuffer | null = null;

  /** 网络质量监控器 */
  private networkQualityMonitor: NetworkQualityMonitor | null = null;

  /** 数据压缩器 */
  private dataCompressor: DataCompressor | null = null;

  /** 服务发现客户端 */
  private serviceDiscoveryClient: ServiceDiscoveryClient | null = null;

  /** 网络预测器 */
  private networkPredictor: NetworkPredictor | null = null;

  /** 网络自适应控制器 */
  private networkAdaptiveController: NetworkAdaptiveController | null = null;

  /** 高级带宽控制器 */
  private bandwidthController: AdvancedBandwidthController | null = null;

  /** 空间分区系统 */
  private spatialPartitioning: QuadtreePartitioning | null = null;

  /** 网络实体映射表 */
  private networkEntities: Map<string, Entity> = new Map();

  /**
   * 创建增强型网络系统
   * @param options 配置
   */
  constructor(options: EnhancedNetworkSystemConfig = {}) {
    super(1); // 优先级1

    // 默认配置
    this.options = {
      enabled: true,
      localUserId: '',
      syncInterval: 100,
      maxReconnectAttempts: 5,
      reconnectInterval: 3000,
      enableCompression: true,
      compressionAlgorithm: CompressionAlgorithm.LZ_STRING,
      compressionLevel: CompressionLevel.MEDIUM,
      enableMediaStream: true,
      enableAudio: false,
      enableVideo: false,
      enableScreenShare: false,
      enableNetworkQualityMonitor: true,
      enableBandwidthControl: true,
      bandwidthControlStrategy: BandwidthControlStrategy.ADAPTIVE,
      maxUploadBandwidth: 1024 * 1024, // 1MB/s
      maxDownloadBandwidth: 1024 * 1024, // 1MB/s
      enableEntitySync: true,
      enableUserSessionManagement: true,
      defaultUserRole: UserRole.USER,
      enablePermissionCheck: true,
      enableEventBuffer: true,
      enableEventLogging: false,
      enableServiceDiscovery: true,
      serviceRegistryUrl: 'http://localhost:4010/api/registry',
      enableMicroserviceClient: true,
      apiGatewayUrl: 'http://localhost:3000/api',
      useApiGateway: true,
      enablePrediction: true,
      predictionAlgorithm: PredictionAlgorithm.ADAPTIVE,
      maxPredictionTime: 200,
      enableInterpolation: true,
      interpolationFactor: 0.5,
      enableSpatialPartitioning: true,
      spatialPartitioningMaxDepth: 8,
      spatialPartitioningMaxEntities: 16,
      enableAdaptiveControl: true,
      adaptiveStrategy: AdaptiveStrategy.BALANCED,
      enableJitterBuffer: true,
      jitterBufferSize: 100,
      enablePrioritySync: true,
      enableDeltaSync: true,
      ...options,
    };

    // 设置本地用户ID
    this.localUserId = this.options.localUserId || null;

    // 初始化系统
    this.initialize();
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    if (!this.options.enabled) {
      Debug.log('EnhancedNetworkSystem', '网络系统已禁用');
      return;
    }

    // 初始化数据压缩器
    if (this.options.enableCompression) {
      this.dataCompressor = new DataCompressor({
        algorithm: this.options.compressionAlgorithm,
        level: this.options.compressionLevel,
      });
    }

    // 初始化网络质量监控器
    if (this.options.enableNetworkQualityMonitor) {
      this.networkQualityMonitor = new NetworkQualityMonitor({
        sampleInterval: 1000,
        historySize: 10,
        pingInterval: 2000,
      });

      // 监听网络质量变化
      this.networkQualityMonitor.on('qualityChanged', (quality: NetworkQualityData) => {
        this.handleNetworkQualityChange(quality);
      });
    }

    // 初始化带宽控制器
    if (this.options.enableBandwidthControl) {
      this.bandwidthController = new AdvancedBandwidthController({
        maxUploadBandwidth: this.options.maxUploadBandwidth,
        maxDownloadBandwidth: this.options.maxDownloadBandwidth,
        strategy: this.options.bandwidthControlStrategy,
        autoAdjust: true,
      });
    }

    // 初始化网络预测器
    if (this.options.enablePrediction) {
      this.networkPredictor = new NetworkPredictor({
        algorithm: this.options.predictionAlgorithm,
        maxPredictionTime: this.options.maxPredictionTime,
        useSmoothing: true,
        smoothingFactor: 0.3,
        useAdaptivePrediction: true,
        useJitterBuffer: this.options.enableJitterBuffer,
        jitterBufferSize: this.options.jitterBufferSize,
      });
    }

    // 初始化空间分区系统
    if (this.options.enableSpatialPartitioning) {
      this.spatialPartitioning = new QuadtreePartitioning({
        maxDepth: this.options.spatialPartitioningMaxDepth,
        maxEntities: this.options.spatialPartitioningMaxEntities,
        worldSize: 1000,
        enableDynamicAdjustment: true,
      });
    }

    // 初始化网络事件缓冲
    if (this.options.enableEventBuffer) {
      this.eventBuffer = new NetworkEventBuffer({
        maxBufferSize: 100,
      });
    }

    // 初始化网络事件调度器
    this.eventDispatcher = new NetworkEventDispatcher({
      enableEventLogging: this.options.enableEventLogging,
      useEventBuffer: !!this.eventBuffer,
    });

    // 初始化用户会话管理器
    if (this.options.enableUserSessionManagement) {
      this.userSessionManager = new UserSessionManager({
        defaultRole: this.options.defaultUserRole,
        enablePermissionCheck: this.options.enablePermissionCheck,
      });
    }

    // 初始化实体同步管理器
    if (this.options.enableEntitySync) {
      this.entitySyncManager = new EntitySyncManager({
        minSyncInterval: this.options.syncInterval,
        useDeltaSync: this.options.enableDeltaSync,
        usePrioritySync: this.options.enablePrioritySync,
        useSpatialPartitioning: this.options.enableSpatialPartitioning,
        useInterpolation: this.options.enableInterpolation,
        useExtrapolation: this.options.enablePrediction,
        extrapolationTime: this.options.maxPredictionTime,
      });

      // 设置空间分区系统（如果支持）
      if (this.spatialPartitioning && typeof (this.entitySyncManager as any).setSpatialPartitioning === 'function') {
        (this.entitySyncManager as any).setSpatialPartitioning(this.spatialPartitioning);
      }

      // 设置网络预测器（如果支持）
      if (this.networkPredictor && typeof (this.entitySyncManager as any).setNetworkPredictor === 'function') {
        (this.entitySyncManager as any).setNetworkPredictor(this.networkPredictor);
      }
    }

    // 初始化媒体流管理器
    if (this.options.enableMediaStream) {
      this.mediaStreamManager = new MediaStreamManager({});
    }

    // 初始化网络自适应控制器
    if (this.options.enableAdaptiveControl) {
      // 创建初始网络参数配置
      const initialParams: NetworkParamsConfig = {
        syncInterval: this.options.syncInterval,
        compressionLevel: this.options.compressionLevel,
        useDeltaSync: this.options.enableDeltaSync,
        usePrediction: this.options.enablePrediction,
        predictionTime: this.options.maxPredictionTime,
        useInterpolation: this.options.enableInterpolation,
        interpolationFactor: this.options.interpolationFactor,
        usePrioritySync: this.options.enablePrioritySync,
        useSpatialPartitioning: this.options.enableSpatialPartitioning,
        useJitterBuffer: this.options.enableJitterBuffer,
        jitterBufferSize: this.options.jitterBufferSize,
        maxUploadBandwidth: this.options.maxUploadBandwidth,
        maxDownloadBandwidth: this.options.maxDownloadBandwidth,
      };

      this.networkAdaptiveController = new NetworkAdaptiveController(initialParams, {
        strategy: this.options.adaptiveStrategy,
        enableAutoAdjust: true,
        enableHistory: true,
        enablePredictiveAdjustment: true,
      });

      // 设置带宽控制器（使用类型断言）
      if (this.bandwidthController) {
        (this.networkAdaptiveController as any).setBandwidthController?.(this.bandwidthController);
      }

      // 设置实体同步管理器
      if (this.entitySyncManager) {
        (this.networkAdaptiveController as any).setEntitySyncManager?.(this.entitySyncManager);
      }

      // 监听参数调整事件
      this.networkAdaptiveController.on('paramsAdjusted', (data) => {
        this.handleNetworkParamsAdjusted(data.params);
      });
    }

    // 初始化服务发现客户端
    if (this.options.enableServiceDiscovery) {
      this.serviceDiscoveryClient = new ServiceDiscoveryClient({
        registryUrl: this.options.serviceRegistryUrl,
      });
    }

    // 初始化WebRTC连接管理器
    this.networkManager = new WebRTCConnectionManager({
      enableDataChannel: true,
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' },
      ],
    });

    // 监听连接事件
    this.networkManager.on('connected', (peerId: string) => {
      this.handlePeerConnected(peerId);
    });

    this.networkManager.on('disconnected', (peerId: string) => {
      this.handlePeerDisconnected(peerId);
    });

    this.networkManager.on('dataReceived', (peerId: string, data: any) => {
      this.handleDataReceived(peerId, data);
    });

    // 启动同步定时器
    this.startSyncTimer();

    Debug.log('EnhancedNetworkSystem', '网络系统已初始化');
  }

  /**
   * 启动同步定时器
   */
  private startSyncTimer(): void {
    if (this.syncTimerId !== null) {
      return;
    }

    this.syncTimerId = window.setInterval(() => {
      this.syncEntities();
    }, this.options.syncInterval);
  }

  /**
   * 停止同步定时器
   */
  private stopSyncTimer(): void {
    if (this.syncTimerId !== null) {
      clearInterval(this.syncTimerId);
      this.syncTimerId = null;
    }
  }

  /**
   * 同步实体
   */
  private syncEntities(): void {
    if (!this.entitySyncManager || this.state !== NetworkState.CONNECTED) {
      return;
    }

    // 使用类型断言调用私有方法
    (this.entitySyncManager as any).syncEntities?.();
  }

  /**
   * 处理网络质量变化
   * @param quality 网络质量数据
   */
  private handleNetworkQualityChange(quality: NetworkQualityData): void {
    // 更新带宽控制器
    if (this.bandwidthController) {
      (this.bandwidthController as any).setNetworkQuality?.(quality);
    }

    // 更新网络自适应控制器
    if (this.networkAdaptiveController) {
      this.networkAdaptiveController.setNetworkQuality(quality);
    }

    // 触发网络质量变化事件
    this.eventEmitter.emit('networkQualityChanged', quality);
  }

  /**
   * 处理网络参数调整
   * @param params 网络参数
   */
  private handleNetworkParamsAdjusted(params: NetworkParamsConfig): void {
    // 更新同步间隔
    if (params.syncInterval !== this.options.syncInterval) {
      this.options.syncInterval = params.syncInterval;

      // 重启同步定时器
      this.stopSyncTimer();
      this.startSyncTimer();
    }

    // 更新压缩级别
    if (params.compressionLevel !== this.options.compressionLevel) {
      this.options.compressionLevel = params.compressionLevel;

      if (this.dataCompressor) {
        // 使用setOptions方法更新压缩级别
        this.dataCompressor.setOptions({ level: params.compressionLevel });
      }
    }

    // 更新预测时间
    if (params.predictionTime !== this.options.maxPredictionTime) {
      this.options.maxPredictionTime = params.predictionTime;
    }

    // 更新插值因子
    if (params.interpolationFactor !== this.options.interpolationFactor) {
      this.options.interpolationFactor = params.interpolationFactor;
    }

    // 更新抖动缓冲大小
    if (params.jitterBufferSize !== this.options.jitterBufferSize) {
      this.options.jitterBufferSize = params.jitterBufferSize;
    }

    // 触发参数调整事件
    this.eventEmitter.emit('networkParamsAdjusted', params);
  }

  /**
   * 处理对等连接
   * @param peerId 对等ID
   */
  private handlePeerConnected(peerId: string): void {
    Debug.log('EnhancedNetworkSystem', `对等连接已建立: ${peerId}`);

    // 更新状态
    this.state = NetworkState.CONNECTED;

    // 重置重连尝试次数
    this.reconnectAttempts = 0;

    // 添加用户会话
    if (this.userSessionManager) {
      (this.userSessionManager as any).addUser?.(peerId, this.options.defaultUserRole);
    }

    // 触发连接事件
    this.eventEmitter.emit('peerConnected', peerId);
  }

  /**
   * 处理对等断开连接
   * @param peerId 对等ID
   */
  private handlePeerDisconnected(peerId: string): void {
    Debug.log('EnhancedNetworkSystem', `对等连接已断开: ${peerId}`);

    // 移除用户会话
    if (this.userSessionManager) {
      (this.userSessionManager as any).removeUser?.(peerId);
    }

    // 移除实体
    if (this.entitySyncManager) {
      (this.entitySyncManager as any).removeRemoteEntities?.(peerId);
    }

    // 触发断开连接事件
    this.eventEmitter.emit('peerDisconnected', peerId);

    // 如果没有连接的对等点，则更新状态
    if (this.networkManager && (this.networkManager as any).getPeerCount?.() === 0) {
      this.state = NetworkState.DISCONNECTED;

      // 尝试重连
      this.tryReconnect();
    }
  }

  /**
   * 处理接收到的数据
   * @param peerId 对等ID
   * @param data 数据
   */
  private handleDataReceived(peerId: string, data: any): void {
    // 解压数据
    let decompressedData = data;
    if (this.options.enableCompression && this.dataCompressor && typeof data === 'string') {
      try {
        decompressedData = this.dataCompressor.decompress(data);
      } catch (error) {
        Debug.error('EnhancedNetworkSystem', `解压数据失败: ${error}`);
        return;
      }
    }

    // 处理事件
    if (this.eventDispatcher) {
      (this.eventDispatcher as any).handleIncomingEvent?.(peerId, decompressedData);
    }

    // 处理实体同步
    if (this.entitySyncManager) {
      (this.entitySyncManager as any).handleSyncData?.(peerId, decompressedData);
    }

    // 触发数据接收事件
    this.eventEmitter.emit('dataReceived', peerId, decompressedData);
  }

  /**
   * 尝试重连
   */
  private tryReconnect(): void {
    if (this.reconnectAttempts >= this.options.maxReconnectAttempts) {
      Debug.warn('EnhancedNetworkSystem', '已达到最大重连尝试次数');
      return;
    }

    if (this.reconnectTimerId !== null) {
      return;
    }

    this.reconnectAttempts++;

    Debug.log('EnhancedNetworkSystem', `尝试重连 (${this.reconnectAttempts}/${this.options.maxReconnectAttempts})`);

    this.reconnectTimerId = window.setTimeout(() => {
      this.reconnectTimerId = null;

      if (this.networkManager) {
        (this.networkManager as any).reconnect?.();
      }
    }, this.options.reconnectInterval);
  }

  /**
   * 系统更新
   * @param deltaTime 时间增量
   */
  public update(deltaTime: number): void {
    if (!this.options.enabled || this.state === NetworkState.DISCONNECTED) {
      return;
    }

    // 更新网络质量监控器
    if (this.networkQualityMonitor) {
      (this.networkQualityMonitor as any).update?.(deltaTime);
    }

    // 更新实体同步管理器
    if (this.entitySyncManager) {
      (this.entitySyncManager as any).update?.(deltaTime);
    }

    // 更新网络事件调度器
    if (this.eventDispatcher) {
      (this.eventDispatcher as any).update?.(deltaTime);
    }
  }

  /**
   * 连接到服务器
   * @param serverUrl 服务器URL
   */
  public connect(serverUrl: string): void {
    if (!this.networkManager) {
      Debug.error('EnhancedNetworkSystem', '网络管理器未初始化');
      return;
    }

    if (this.state === NetworkState.CONNECTING || this.state === NetworkState.CONNECTED) {
      Debug.warn('EnhancedNetworkSystem', '已经在连接或已连接');
      return;
    }

    this.state = NetworkState.CONNECTING;

    Debug.log('EnhancedNetworkSystem', `正在连接到服务器: ${serverUrl}`);

    (this.networkManager as any).connect?.(serverUrl);
  }

  /**
   * 断开连接
   */
  public disconnect(): void {
    if (!this.networkManager) {
      return;
    }

    if (this.state === NetworkState.DISCONNECTED) {
      return;
    }

    Debug.log('EnhancedNetworkSystem', '正在断开连接');

    (this.networkManager as any).disconnect?.();
    this.state = NetworkState.DISCONNECTED;

    // 停止重连
    if (this.reconnectTimerId !== null) {
      clearTimeout(this.reconnectTimerId);
      this.reconnectTimerId = null;
    }
  }

  /**
   * 发送数据到所有对等点
   * @param data 数据
   * @param compress 是否压缩
   */
  public sendToAll(data: any, compress: boolean = this.options.enableCompression): void {
    if (!this.networkManager || this.state !== NetworkState.CONNECTED) {
      return;
    }

    let processedData = data;

    // 压缩数据
    if (compress && this.dataCompressor) {
      try {
        processedData = this.dataCompressor.compress(data);
      } catch (error) {
        Debug.error('EnhancedNetworkSystem', `压缩数据失败: ${error}`);
        return;
      }
    }

    // 记录带宽使用
    if (this.bandwidthController) {
      const dataSize = typeof processedData === 'string' ? processedData.length : JSON.stringify(processedData).length;
      (this.bandwidthController as any).recordUpload?.(dataSize);
    }

    (this.networkManager as any).sendToAll?.(processedData);
  }

  /**
   * 发送数据到特定对等点
   * @param peerId 对等ID
   * @param data 数据
   * @param compress 是否压缩
   */
  public async sendToPeer(peerId: string, data: any, compress: boolean = this.options.enableCompression): Promise<void> {
    if (!this.networkManager || this.state !== NetworkState.CONNECTED) {
      return;
    }

    let processedData = data;

    // 压缩数据
    if (compress && this.dataCompressor) {
      try {
        const result = await this.dataCompressor.compress(data);
        processedData = result.data;
      } catch (error) {
        Debug.error('EnhancedNetworkSystem', `压缩数据失败: ${error}`);
        return;
      }
    }

    // 记录带宽使用
    if (this.bandwidthController) {
      const dataSize = typeof processedData === 'string' ? processedData.length : JSON.stringify(processedData).length;
      (this.bandwidthController as any).recordUpload?.(dataSize);
    }

    (this.networkManager as any).sendToPeer?.(peerId, processedData);
  }

  /**
   * 注册网络实体
   * @param entity 实体
   * @param entityType 实体类型
   * @param syncMode 同步模式
   */
  public registerNetworkEntity(
    entity: Entity,
    entityType: NetworkEntityType = NetworkEntityType.DYNAMIC,
    syncMode: NetworkEntitySyncMode = NetworkEntitySyncMode.FULL
  ): void {
    if (!this.entitySyncManager) {
      Debug.error('EnhancedNetworkSystem', '实体同步管理器未初始化');
      return;
    }

    // 添加网络实体组件
    if (!entity.hasComponent('NetworkEntity')) {
      entity.addComponent(new NetworkEntityComponent({
        entityId: entity.id,
        ownerId: this.localUserId || 'unknown'
      }));
    }

    // 注册到同步管理器
    (this.entitySyncManager as any).registerEntity?.(entity);

    // 添加到空间分区
    if (this.spatialPartitioning && entity.hasComponent('Transform')) {
      this.spatialPartitioning.addEntity(entity.id, entity);
    }

    // 添加到映射表
    this.networkEntities.set(entity.id, entity);

    // 使用参数避免未使用警告
    Debug.log('EnhancedNetworkSystem', `注册网络实体: ${entity.id}, 类型: ${entityType}, 同步模式: ${syncMode}`);
  }

  /**
   * 注销网络实体
   * @param entityId 实体ID
   */
  public unregisterNetworkEntity(entityId: string): void {
    if (!this.entitySyncManager) {
      return;
    }

    // 从同步管理器注销
    (this.entitySyncManager as any).unregisterEntity?.(entityId);

    // 从空间分区移除
    if (this.spatialPartitioning) {
      this.spatialPartitioning.removeEntity(entityId);
    }

    // 从映射表移除
    this.networkEntities.delete(entityId);
  }

  /**
   * 获取网络实体
   * @param entityId 实体ID
   * @returns 实体
   */
  public getNetworkEntity(entityId: string): Entity | undefined {
    return this.networkEntities.get(entityId);
  }

  /**
   * 获取所有网络实体
   * @returns 实体映射表
   */
  public getAllNetworkEntities(): Map<string, Entity> {
    return new Map(this.networkEntities);
  }

  /**
   * 查询区域内的网络实体
   * @param minX 最小X坐标
   * @param minZ 最小Z坐标
   * @param maxX 最大X坐标
   * @param maxZ 最大Z坐标
   * @returns 实体映射表
   */
  public queryNetworkEntitiesInRegion(minX: number, minZ: number, maxX: number, maxZ: number): Map<string, Entity> {
    if (!this.spatialPartitioning) {
      return new Map();
    }

    return this.spatialPartitioning.queryRegion(minX, minZ, maxX, maxZ);
  }

  /**
   * 查询半径内的网络实体
   * @param x 中心X坐标
   * @param z 中心Z坐标
   * @param radius 半径
   * @returns 实体映射表
   */
  public queryNetworkEntitiesInRadius(x: number, z: number, radius: number): Map<string, Entity> {
    if (!this.spatialPartitioning) {
      return new Map();
    }

    return this.spatialPartitioning.queryRadius(x, z, radius);
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param listener 监听器
   */
  public addEventListener(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param listener 监听器
   */
  public removeEventListener(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }

  /**
   * 获取网络状态
   * @returns 网络状态
   */
  public getState(): NetworkState {
    return this.state;
  }

  /**
   * 获取本地用户ID
   * @returns 本地用户ID
   */
  public getLocalUserId(): string | null {
    return this.localUserId;
  }

  /**
   * 获取网络质量
   * @returns 网络质量数据
   */
  public getNetworkQuality(): NetworkQualityData | null {
    if (!this.networkQualityMonitor) {
      return null;
    }

    return (this.networkQualityMonitor as any).getLatestQuality?.() || null;
  }

  /**
   * 获取服务发现客户端
   * @returns 服务发现客户端
   */
  public getServiceDiscoveryClient(): ServiceDiscoveryClient | null {
    return this.serviceDiscoveryClient;
  }

  /**
   * 获取带宽使用
   * @returns 带宽使用数据
   */
  public getBandwidthUsage(): any {
    if (!this.bandwidthController) {
      return null;
    }

    return this.bandwidthController.getBandwidthUsage();
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 停止同步定时器
    this.stopSyncTimer();

    // 停止重连定时器
    if (this.reconnectTimerId !== null) {
      clearTimeout(this.reconnectTimerId);
      this.reconnectTimerId = null;
    }

    // 断开连接
    this.disconnect();

    // 销毁各个管理器
    if (this.networkManager) {
      (this.networkManager as any).dispose();
    }

    if (this.mediaStreamManager) {
      (this.mediaStreamManager as any).dispose();
    }

    if (this.entitySyncManager) {
      (this.entitySyncManager as any).dispose();
    }

    if (this.userSessionManager) {
      (this.userSessionManager as any).dispose();
    }

    if (this.eventDispatcher) {
      (this.eventDispatcher as any).dispose();
    }

    if (this.networkQualityMonitor) {
      (this.networkQualityMonitor as any).dispose();
    }

    if (this.bandwidthController) {
      (this.bandwidthController as any).dispose();
    }

    if (this.networkAdaptiveController) {
      (this.networkAdaptiveController as any).dispose();
    }

    if (this.spatialPartitioning) {
      this.spatialPartitioning.clear();
    }

    // 清空实体映射表
    this.networkEntities.clear();

    // 移除所有事件监听器
    this.eventEmitter.removeAllListeners();

    Debug.log('EnhancedNetworkSystem', '网络系统已销毁');
  }
}
